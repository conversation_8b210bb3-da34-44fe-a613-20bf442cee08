import React from 'react';

const FullscreenModal = ({ onAccept, onDecline }) => {
    return (
        <div className="fixed inset-0 flex items-center justify-center z-50 bg-black/40 backdrop-blur-sm">
            <div className="bg-white p-3 rounded-lg shadow-lg max-w-[227px] mx-2 relative">
                {/* X button positioned absolutely in the top right corner */}
                <button
                    onClick={onDecline}
                    className="absolute top-2 right-2 text-gray-500 hover:text-gray-700 transition-colors"
                    aria-label="Close"
                >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>

                <div className="flex justify-center mb-3 pt-3">
                    <svg width="18" height="18" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M20.8333 3.125L16.6667 3.125C16.0906 3.125 15.625 3.59167 15.625 4.16667C15.625 4.74167 16.0906 5.20833 16.6667 5.20833L18.551 5.20833L14.8885 8.89167C14.4812 9.29896 14.4812 9.95729 14.8885 10.3646C15.0917 10.5677 15.3583 10.6698 15.625 10.6698C15.8917 10.6698 16.1583 10.5677 16.3615 10.3646L19.7917 6.91562V8.33333C19.7917 8.90833 20.2573 9.375 20.8333 9.375C21.4094 9.375 21.875 8.90833 21.875 8.33333V4.16667C21.875 3.59167 21.4094 3.125 20.8333 3.125Z" fill="black" />
                        <path d="M20.8333 15.625C20.2573 15.625 19.7917 16.0917 19.7917 16.6667V18.3302L16.3615 14.8885C15.9542 14.4812 15.2958 14.4812 14.8885 14.8885C14.4812 15.2958 14.4812 15.9542 14.8885 16.3615L18.3073 19.7917H16.6667C16.0906 19.7917 15.625 20.2583 15.625 20.8333C15.625 21.4083 16.0906 21.875 16.6667 21.875H20.8333C21.4094 21.875 21.875 21.4083 21.875 20.8333V16.6667C21.875 16.0917 21.4094 15.625 20.8333 15.625Z" fill="black" />
                        <path d="M6.68125 5.20833L8.33333 5.20833C8.90937 5.20833 9.375 4.74167 9.375 4.16667C9.375 3.59167 8.90937 3.125 8.33333 3.125L4.16667 3.125C4.03125 3.125 3.89583 3.15312 3.76875 3.20521C3.51354 3.31042 3.31042 3.51354 3.20521 3.76875C3.15312 3.89583 3.125 4.03125 3.125 4.16667L3.125 8.33333C3.125 8.90833 3.59063 9.375 4.16667 9.375C4.74271 9.375 5.20833 8.90833 5.20833 8.33333L5.20833 6.68125L8.89062 10.3646C9.09375 10.5677 9.36042 10.6698 9.62708 10.6698C9.89375 10.6698 10.1604 10.5677 10.3635 10.3646C10.7708 9.95729 10.7708 9.29896 10.3635 8.89167L6.68125 5.20833Z" fill="black" />
                        <path d="M8.89062 14.8885L5.20833 18.5708L5.20833 16.6667C5.20833 16.0917 4.74271 15.625 4.16667 15.625C3.59063 15.625 3.125 16.0917 3.125 16.6667L3.125 20.8333C3.125 21.4083 3.59063 21.875 4.16667 21.875H8.33333C8.90937 21.875 9.375 21.4083 9.375 20.8333C9.375 20.2583 8.90937 19.7917 8.33333 19.7917H6.93333L10.3635 16.3615C10.7708 15.9542 10.7708 15.2958 10.3635 14.8885C9.95625 14.4812 9.29792 14.4812 8.89062 14.8885Z" fill="black" />
                    </svg>
                </div>

                <p className="text-[14px] font-medium text-center mb-2">Switch to full screen for the best experience</p>

                <div className="mt-2 mb-2 flex justify-center">
                    <button
                        onClick={()=>{
                            onAccept();onDecline()}}
                        className="px-8 py-2 bg-blue-600 text-white rounded-lg font-medium w-full sm:w-auto"
                    >
                        Switch
                    </button>
                </div>
            </div>
        </div>
    );
};

export default FullscreenModal;