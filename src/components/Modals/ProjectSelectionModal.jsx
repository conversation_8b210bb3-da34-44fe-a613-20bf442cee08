import { useState, useEffect, useRef } from 'react';
import { PostRequestWithHeaders } from '../../Tools/helpers/api';
import BottomSheet from '../common/BottomSheet';
import { getScreenSize, addScreenResizeListener } from '../../utils/screenUtils';

const ProjectSelectionModal = ({ isOpen, onClose, onSelectProject, currentProjectId, currentProjectType }) => {
  const [activeFilter, setActiveFilter] = useState('all');
  const [locationFilter, setLocationFilter] = useState('all');
  const [projects, setProjects] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedProject, setSelectedProject] = useState(null);
  const [switchingProject, setSwitchingProject] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [hasInitiallyLoaded, setHasInitiallyLoaded] = useState(false);

  // Check if it's mobile/tablet screen
  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 1024); // lg breakpoint
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  useEffect(() => {
    if (isOpen) {
      // Reset states when modal opens
      setSelectedProject(null);
      setError(null);
      setSearchQuery('');
      
      // Only fetch if we haven't loaded before or if projects array is empty
      if (!hasInitiallyLoaded || projects.length === 0) {
        fetchProjects();
      }
    }
  }, [isOpen, hasInitiallyLoaded, projects.length]);

  const fetchProjects = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log("Fetching projects from API...");
      console.log("API URL:", `${process.env.REACT_APP_API_BACKEND_API_URL}org/ListProjectsFromOrganization`);

      // Using the PostRequestWithHeaders helper
      const response = await PostRequestWithHeaders({
        url: `${process.env.REACT_APP_API_BACKEND_API_URL}org/ListProjectsFromOrganization`,
      });

      console.log("API response received:", response);
      console.log("Response type:", typeof response);

      // Check if the response has the expected format
      if (response) {
        let projectsArray = [];

        console.log("Response structure:", Object.keys(response));

        // Handle the specific response structure with projectsObj
        if (response.projectsObj) {
          console.log("Found projectsObj with keys:", Object.keys(response.projectsObj));
          projectsArray = Object.values(response.projectsObj);
        }
        // Handle case where response is the data property containing projectsObj
        else if (response.data && response.data.projectsObj) {
          console.log("Found data.projectsObj with keys:", Object.keys(response.data.projectsObj));
          projectsArray = Object.values(response.data.projectsObj);
        }
        // Handle case where response is already an array
        else if (Array.isArray(response)) {
          console.log("Response is an array with length:", response.length);
          projectsArray = response;
        }
        // Handle case where response is a simple object of projects
        else if (typeof response === 'object') {
          console.log("Response is an object with keys:", Object.keys(response));
          projectsArray = Object.values(response);
        }

        // Filter out any null or undefined values
        const validProjects = projectsArray.filter(project => project != null);
        console.log("Valid projects count:", validProjects.length);

        // Log the first project to see its structure
        if (validProjects.length > 0) {
          console.log("Sample project structure:", validProjects[0]);
        }

        setProjects(validProjects);
        setHasInitiallyLoaded(true);
      } else {
        console.warn("Unexpected API response structure:", response);
        setProjects([]);
        setHasInitiallyLoaded(true);
      }

      setLoading(false);
    } catch (error) {
      console.error("Error fetching projects:", error);
      setError("Failed to load projects. Please try again.");
      setProjects([]);
      setLoading(false);
      setHasInitiallyLoaded(true);
    }
  };

  const experienceCards = projects.flatMap(project => {
    const matchesSearch = project?.name?.toLowerCase().includes(searchQuery.toLowerCase());
    if (!matchesSearch) return [];

    const cards = [];

    if (project.projectSettings?.pixelstreaming?.is_enabled) {
      cards.push({
        ...project,
        experienceType: 'pixelstreaming'
      });
    }

    if (project.projectSettings?.ale?.is_enabled) {
      cards.push({
        ...project,
        experienceType: 'ale'
      });
    }

    return cards;
  });

  // Get unique locations from projects
  const uniqueLocations = [...new Set(projects.map(project => project.country).filter(Boolean))];

  // Updated filter handler to include location
  const filteredExperienceCards = experienceCards.filter(project => {
    const matchesType = activeFilter === 'all' ? true : project.experienceType === activeFilter;
    const matchesLocation = locationFilter === 'all' ? true : project.country === locationFilter;
    return matchesType && matchesLocation;
  });

  const handleProjectSelect = (project) => {
    // Don't allow selection of currently active project
    if (isCurrentlyShared(project)) {
      return;
    }
    console.log("Selected project:", project);
    setSelectedProject(project);
  };

  // Check if a project is currently being shared
  const isCurrentlyShared = (project) => {
    if (!currentProjectId || !currentProjectType) return false;

    // Check if this is the same project and experience type
    const isSameProject = project._id === currentProjectId;
    const isSameExperienceType =
      (project.experienceType === 'pixelstreaming' && (currentProjectType === 'lark' || currentProjectType === 'pixel_streaming')) ||
      (project.experienceType === 'ale' && currentProjectType === 'ale');

    return isSameProject && isSameExperienceType;
  };

  // Get project status for display
  const getProjectStatus = (project) => {
    if (isCurrentlyShared(project)) {
      return 'Currently Shared';
    }
    return null;
  };

  const handleShareButtonClick = () => {
    if (!selectedProject) return;

    setSwitchingProject(true);

    // Directly call the parent's onSelectProject without the switch API
    setTimeout(() => {
      onSelectProject(selectedProject);
      setSwitchingProject(false);
      onClose();
    }, 500); // Small delay for UI feedback
  };

  // Content component that will be used in both desktop modal and mobile bottom sheet
  const ModalContent = () => (
    <div className="flex flex-col h-full">
      {/* Search and Filters Section */}
      <div className="mb-2 px-2 lg:px-0">
        <div className="flex gap-2">
          {/* Search Input */}
          <div className="relative flex-1">
            <div className={`absolute left-3 top-1/2 -translate-y-1/2 ${loading ? 'text-gray-400' : 'text-gray-500'}`}>
              {loading ? (
                <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
              ) : (
                <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12.0078 12.21C12.0843 12.2094 12.1579 12.2381 12.2139 12.29L15.4121 15.4883C15.454 15.5302 15.4826 15.5835 15.4941 15.6416C15.5057 15.6996 15.5001 15.7598 15.4775 15.8145C15.4548 15.8693 15.4155 15.9163 15.3662 15.9492C15.317 15.9821 15.2594 16 15.2002 16L15.1416 15.9941C15.0839 15.9826 15.0305 15.9543 14.9883 15.9121L11.7939 12.7178C11.7395 12.6612 11.7093 12.5853 11.71 12.5068C11.7107 12.4283 11.7424 12.3534 11.7979 12.2979C11.8535 12.2422 11.9291 12.2106 12.0078 12.21ZM5.24902 1.11328C6.39352 0.885631 7.58011 1.00266 8.6582 1.44922C9.73615 1.89577 10.6574 2.65196 11.3057 3.62207C11.9135 4.5317 12.2555 5.59123 12.2959 6.68164L12.2998 6.90039C12.2978 8.46423 11.676 9.96341 10.5703 11.0693C9.46411 12.1755 7.96382 12.7971 6.39941 12.7988C5.23272 12.7987 4.09215 12.4538 3.12207 11.8057C2.15195 11.1574 1.39579 10.2361 0.949219 9.1582C0.502666 8.08014 0.385645 6.89349 0.613281 5.74902C0.840936 4.60454 1.40338 3.55364 2.22852 2.72852C3.05366 1.90339 4.10452 1.34093 5.24902 1.11328ZM6.40039 1.59961C5.35214 1.59961 4.32667 1.91079 3.45508 2.49316C2.58363 3.07552 1.90501 3.90373 1.50391 4.87207C1.10283 5.84039 0.997735 6.90562 1.20215 7.93359C1.40664 8.96164 1.91118 9.90626 2.65234 10.6475C3.39357 11.3887 4.3381 11.8941 5.36621 12.0986C6.39426 12.3031 7.46032 12.198 8.42871 11.7969C9.39697 11.3958 10.2243 10.7161 10.8066 9.84473C11.3889 8.97326 11.7001 7.9485 11.7002 6.90039V6.89941C11.6988 5.49436 11.14 4.14684 10.1465 3.15332C9.15294 2.15987 7.80541 1.60101 6.40039 1.59961Z" fill="#6B7280" stroke="#6B7280"/>
                </svg>
              )}
            </div>
            <input
              type="text"
              placeholder={loading ? "Loading projects..." : "Search projects..."}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              disabled={loading}
              className={`lg:w-[70%] max-sm:w-[100%] pl-9 pr-2.5 py-2 border border-gray-200 rounded-lg text-xs outline-none transition-all ${
                loading
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed placeholder:text-gray-400'
                  : 'bg-gray-50 placeholder:text-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent'
              }`}
            />
          </div>

          {/* Experience Type Dropdown */}
          <div className="relative">
            <select
              value={activeFilter}
              onChange={(e) => setActiveFilter(e.target.value)}
              className="appearance-none md:min-w-[120px] text-xs border border-gray-200 rounded-lg bg-gray-50 px-2.5 py-2 pr-8 outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">Experience Type</option>
              <option value="pixelstreaming">Metaverse</option>
              <option value="ale">ALE</option>
            </select>
            <div className="absolute right-2.5 top-1/2 -translate-y-1/2 pointer-events-none">
              <svg width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M1 1L5 5L9 1" stroke="#6B7280" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
          </div>

          {/* Location Dropdown */}
          <div className="relative">
            <select
              value={locationFilter}
              onChange={(e) => setLocationFilter(e.target.value)}
              className="appearance-none md:min-w-[120px] text-xs border border-gray-200 rounded-lg bg-gray-50 px-2.5 py-2 pr-8 outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">Location</option>
              {uniqueLocations.map(location => (
                <option key={location} value={location}>{location}</option>
              ))}
            </select>
            <div className="absolute right-2.5 top-1/2 -translate-y-1/2 pointer-events-none">
              <svg width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M1 1L5 5L9 1" stroke="#6B7280" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex-1 py-1 px-2 lg:px-0">
          {/* Skeleton Grid for better UX */}
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-2.5 sm:gap-3">
            {[...Array(8)].map((_, index) => (
              <div
                key={index}
                className="relative rounded-lg overflow-hidden h-28 sm:h-32 bg-gray-200 animate-pulse"
              >
                <div className="absolute inset-0 bg-gray-300">
                  <div className="absolute bottom-2 left-2 right-2 space-y-1">
                    <div className="h-3 bg-gray-400 rounded"></div>
                    <div className="h-2 bg-gray-400 rounded w-2/3"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Error State */}
      {!loading && error && (
        <div className="flex-1 py-1 px-2 lg:px-0">
          <div className="flex flex-col items-center justify-center py-8 space-y-4">
            <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
              <svg className="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <div className="text-center">
              <p className="text-gray-700 text-sm font-medium mb-1">Unable to load projects</p>
              <p className="text-gray-500 text-xs mb-4">{error}</p>
              <button
                onClick={fetchProjects}
                className="bg-blue-500 hover:bg-blue-600 text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      )}

      {/* No Results Message */}
      {!loading && !error && filteredExperienceCards.length === 0 && (
        <div className="text-center py-6 text-gray-500 text-sm px-2 lg:px-0">
          {searchQuery ? `No projects found matching "${searchQuery}"` : 'No projects available'}
        </div>
      )}

      {/* Projects Grid */}
      {!loading && !error && (
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-2.5 sm:gap-3 overflow-y-auto flex-1 py-1 px-2 lg:px-0">
          {filteredExperienceCards.map((project, index) => {
            const isCurrentProject = isCurrentlyShared(project);
            const isSelected = selectedProject?._id === project._id && selectedProject.experienceType === project.experienceType;
            const projectStatus = getProjectStatus(project);

            return (
              <div
                key={`${project._id}-${project.experienceType}-${index}`}
                onClick={() => handleProjectSelect(project)}
                className={`
                  relative rounded-lg overflow-hidden h-28 sm:h-32 transition-all duration-300
                  ${isCurrentProject
                    ? 'cursor-not-allowed opacity-60 border-2 border-green-500'
                    : 'cursor-pointer hover:shadow-lg border-2'
                  }
                  ${isSelected && !isCurrentProject ? 'border-blue-500' : ''}
                  ${!isSelected && !isCurrentProject ? 'border-transparent hover:border-blue-300' : ''}
                  shadow-[0px_2px_4px_-2px_#0000000D] hover:shadow-[0px_4px_6px_-1px_#0000001A]
                `}
              >
                <div className="relative w-full h-full">
                  <img
                    src={project.project_thumbnail || 'https://firebasestorage.googleapis.com/v0/b/propvr-in-31420.appspot.com/o/CreationtoolAssets%2Fsiteassets%2Fplaceholder.jpg?alt=media'}
                    alt={project.name}
                    className="object-cover w-full h-full"
                  />

                  {/* Gradient Overlay */}
                  <div className={`
                    absolute inset-0 flex flex-col justify-end transition-all duration-300 p-2
                    bg-[linear-gradient(269.83deg,rgba(0,0,0,0)_0.17%,rgba(0,0,0,0.7)_99.88%)]
                    ${isCurrentProject ? 'opacity-90' : ''}
                  `}>
                    {/* Project Type Badge */}
                    <div className="absolute top-2 right-2 backdrop-blur-[40px] bg-black/30 px-2 py-0 rounded-md">
                      <span className="text-white text-[14px] font-medium">
                        {project.experienceType === 'pixelstreaming' ? 'Metaverse' : 'ALE'}
                      </span>
                    </div>

                    {/* Project Name */}
                    <p className="text-white text-xs sm:text-sm font-semibold leading-tight mb-1 line-clamp-2 capitalize">
                      {project.name}
                    </p>

                    {/* Location */}
                    <div className="flex items-center gap-1">
                      <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 13.43a3.12 3.12 0 1 0 0-6.24 3.12 3.12 0 0 0 0 6.24Z" fill="#fff"/>
                        <path d="M12 2C7.6 2 4 5.6 4 10c0 5.4 6.4 11.5 7.3 12.3.2.2.5.3.7.3.2 0 .5-.1.7-.3C13.6 21.5 20 15.4 20 10c0-4.4-3.6-8-8-8Zm0 13.43c-3 0-5.43-2.43-5.43-5.43S9 4.57 12 4.57s5.43 2.43 5.43 5.43-2.43 5.43-5.43 5.43Z" fill="#fff"/>
                      </svg>
                      <span className="text-white text-[10px] font-medium capitalize">
                        {project.country || "Location N/A"}
                      </span>
                    </div>

                    {/* Status Badge */}
                    {projectStatus && (
                      <div className="absolute top-1.5 right-1.5">
                        <span className="bg-green-500 text-white text-xs px-1.5 py-0.5 rounded-full font-medium">
                          {projectStatus}
                        </span>
                      </div>
                    )}

                    {/* Selection Indicator */}
                    {isSelected && !isCurrentProject && (
                      <div className="absolute top-1.5 left-1.5">
                        <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                          <svg width="12" height="12" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M13.5 4.5L6 12L2.5 8.5" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                          </svg>
                        </div>
                      </div>
                    )}

                    {/* Disabled Overlay */}
                    {isCurrentProject && (
                      <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
                        <div className="text-center">
                          <div className="w-6 h-6 mx-auto mb-1 bg-green-500 rounded-full flex items-center justify-center">
                            <svg width="16" height="16" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M16.25 6.25L7.5 15L3.75 11.25" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                            </svg>
                          </div>
                          <p className="text-white text-xs font-medium">Active</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Share Button Section */}
      <div className="mb-1 mt-1 px-2 lg:px-0">
        <div className="flex justify-end items-center">
          <button
            onClick={handleShareButtonClick}
            disabled={!selectedProject || loading || switchingProject}
            className="w-auto max-sm:mt-2 min-w-[213px] min-h-[41px] bg-[#1C64F2] hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white font-medium text-[14px] px-6 rounded-lg transition-all duration-200 flex items-center justify-center gap-2"
          >
            {switchingProject ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                Switching...
              </>
            ) : (
              <>
                Share
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );

  if (!isOpen) return null;

  // Use BottomSheet for mobile/tablet, regular modal for desktop
  if (isMobile) {
    return (
      <BottomSheet
        isOpen={isOpen}
        onClose={onClose}
        title="Select Project"
        height="90vh"
        headerBorder={false}
      >
        <ModalContent />
      </BottomSheet>
    );
  }

  // Desktop modal with fixed width
  return (
    <div
      className="fixed inset-0 z-[10600] flex items-center justify-center bg-black bg-opacity-50"
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div className="bg-white rounded-lg w-[60%] min-w-[800px] max-w-[1200px] px-4 pt-4 max-h-[85vh] flex flex-col">
        {/* Header with close button */}
        <div className="flex justify-between items-center mb-2">
          <div>
            <p className="text-[20px] font-bold text-gray-900">Select Project</p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 p-1.5 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7.0885 6.0001L11.1645 1.92408C11.2381 1.85304 11.2967 1.76807 11.3371 1.67412C11.3774 1.58017 11.3987 1.47912 11.3996 1.37687C11.4005 1.27462 11.381 1.17321 11.3423 1.07857C11.3035 0.983933 11.2464 0.897952 11.1741 0.825648C11.1018 0.753343 11.0158 0.696163 10.9211 0.657442C10.8265 0.618722 10.7251 0.599238 10.6228 0.600127C10.5206 0.601015 10.4195 0.622259 10.3256 0.662618C10.2316 0.702977 10.1467 0.761643 10.0756 0.835193L5.99961 4.91121L1.92359 0.835193C1.77835 0.694917 1.58383 0.617298 1.38192 0.619052C1.18001 0.620807 0.986864 0.701795 0.844085 0.844574C0.701307 0.987352 0.620319 1.1805 0.618564 1.38241C0.616809 1.58432 0.694429 1.77884 0.834705 1.92408L4.91072 6.0001L0.834705 10.0761C0.761155 10.1471 0.702488 10.2321 0.662129 10.3261C0.621771 10.42 0.600527 10.5211 0.599638 10.6233C0.59875 10.7256 0.618234 10.827 0.656954 10.9216C0.695674 11.0163 0.752855 11.1022 0.825159 11.1745C0.897464 11.2469 0.983445 11.304 1.07808 11.3428C1.17272 11.3815 1.27413 11.401 1.37638 11.4001C1.47863 11.3992 1.57968 11.3779 1.67363 11.3376C1.76758 11.2972 1.85256 11.2386 1.92359 11.165L5.99961 7.08898L10.0756 11.165C10.2209 11.3053 10.4154 11.3829 10.6173 11.3811C10.8192 11.3794 11.0124 11.2984 11.1551 11.1556C11.2979 11.0128 11.3789 10.8197 11.3807 10.6178C11.3824 10.4159 11.3048 10.2213 11.1645 10.0761L7.0885 6.0001Z" fill="#9CA3AF"/>
            </svg>
          </button>
        </div>
        
        <ModalContent />
      </div>
    </div>
  );
};

export default ProjectSelectionModal;